#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import struct
import sys

def analyze_biff_file(filename):
    """分析BIFF文件结构"""
    print(f"\n=== 分析文件: {filename} ===")
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
        print(f"文件大小: {len(data)} 字节")
        
        # 检查文件头
        if len(data) < 8:
            print("文件太小，不是有效的BIFF文件")
            return
            
        # 读取前几个字节
        print(f"文件头前16字节: {' '.join(f'{b:02X}' for b in data[:16])}")
        
        # 查找BOF记录
        pos = 0
        record_count = 0
        boundsheet_records = []
        
        while pos < len(data) - 4:
            if pos + 4 > len(data):
                break
                
            # 读取记录头
            record_type = struct.unpack('<H', data[pos:pos+2])[0]
            record_length = struct.unpack('<H', data[pos+2:pos+4])[0]
            
            if record_count < 20:  # 只显示前20个记录
                print(f"记录 #{record_count}: 类型=0x{record_type:04X}, 长度={record_length}")
            
            # 检查BOF记录
            if record_type == 0x0809:  # BOF
                if pos + 8 <= len(data):
                    biff_version = struct.unpack('<H', data[pos+4:pos+6])[0]
                    substream_type = struct.unpack('<H', data[pos+6:pos+8])[0]
                    print(f"  -> BOF记录: BIFF版本=0x{biff_version:04X}, 子流类型=0x{substream_type:04X}")
            
            # 检查BOUNDSHEET记录
            elif record_type == 0x0085:  # BOUNDSHEET
                if pos + 4 + record_length <= len(data):
                    record_data = data[pos+4:pos+4+record_length]
                    if len(record_data) >= 8:
                        bof_offset = struct.unpack('<L', record_data[0:4])[0]
                        sheet_state = record_data[4]
                        sheet_type = record_data[5]
                        name_length = record_data[6] if len(record_data) > 6 else 0
                        
                        # 读取工作表名称
                        sheet_name = ""
                        if len(record_data) > 7:
                            unicode_flag = record_data[7] if len(record_data) > 7 else 0
                            name_start = 8
                            
                            if unicode_flag == 0:  # ANSI
                                if name_start + name_length <= len(record_data):
                                    sheet_name = record_data[name_start:name_start+name_length].decode('latin1', errors='ignore')
                            else:  # Unicode
                                if name_start + name_length * 2 <= len(record_data):
                                    sheet_name = record_data[name_start:name_start+name_length*2].decode('utf-16le', errors='ignore')
                        
                        boundsheet_records.append({
                            'bof_offset': bof_offset,
                            'sheet_state': sheet_state,
                            'sheet_type': sheet_type,
                            'name_length': name_length,
                            'sheet_name': sheet_name
                        })
                        
                        print(f"  -> BOUNDSHEET记录: BOF偏移={bof_offset}, 状态={sheet_state}, 类型={sheet_type}, 名称长度={name_length}, 名称='{sheet_name}'")
            
            # 移动到下一个记录
            pos += 4 + record_length
            record_count += 1
            
            if record_count > 100:  # 限制分析的记录数
                break
        
        print(f"总记录数: {record_count}")
        print(f"BOUNDSHEET记录数: {len(boundsheet_records)}")
        
        # 分析BOUNDSHEET记录指向的位置
        for i, bs in enumerate(boundsheet_records):
            print(f"\n工作表 {i+1}: '{bs['sheet_name']}'")
            print(f"  BOF偏移: {bs['bof_offset']}")
            
            # 检查BOF偏移位置的数据
            if bs['bof_offset'] < len(data) - 8:
                bof_data = data[bs['bof_offset']:bs['bof_offset']+8]
                if len(bof_data) >= 4:
                    bof_type = struct.unpack('<H', bof_data[0:2])[0]
                    bof_length = struct.unpack('<H', bof_data[2:4])[0]
                    print(f"  BOF位置的记录: 类型=0x{bof_type:04X}, 长度={bof_length}")
                    
                    if bof_type == 0x0809 and len(bof_data) >= 8:
                        biff_version = struct.unpack('<H', bof_data[4:6])[0]
                        substream_type = struct.unpack('<H', bof_data[6:8])[0]
                        print(f"  BOF详情: BIFF版本=0x{biff_version:04X}, 子流类型=0x{substream_type:04X}")
                    else:
                        print(f"  警告: BOF偏移位置不是有效的BOF记录!")
            else:
                print(f"  错误: BOF偏移超出文件范围!")
        
    except Exception as e:
        print(f"分析文件时出错: {e}")

if __name__ == "__main__":
    # 分析两个文件
    analyze_biff_file("testww/vv_Monster程序读不了.xls")
    analyze_biff_file("testww/vv_Monster程序正常读取.xls")
