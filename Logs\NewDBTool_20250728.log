[2025-07-28 12:06:00] [淇℃伅] [MainForm] Excel资源强制清理完成
鐢ㄦ埛鎿嶄綔: 资源管理
---
[2025-07-28 12:06:00] [淇℃伅] [MainForm] 打开Excel文件: vv_StdItem.xls
鐢ㄦ埛鎿嶄綔: 用户双击Excel节点
---
[2025-07-28 12:06:00] [淇℃伅] [MainForm] 开始加载Excel文件前的资源清理
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:06:00] [淇℃伅] [MainForm] 检查文件访问权限...
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:06:00] [淇℃伅] [MainForm] 文件访问正常，可以读取
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:06:00] [淇℃伅] [MainForm] 开始读取Excel文件: D:\MirServer\Mud2\DB\vv_StdItem.xls
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 开始POI式BIFF数据解析，文件大小: 143360 字节
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 当前流位置: 0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 开始寻找BIFF数据开始位置...
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 开始搜索BIFF BOF记录($0809)，文件大小: 143360
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 检测到OLE2复合文档格式，尝试提取Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 在OLE2文档中搜索Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 找到Workbook流名称，位置: 1152
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 在Workbook流中找到BIFF BOF记录，位置: 6144
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 成功在OLE2文档中找到Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 找到BIFF数据开始位置: 6144
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 第一阶段：解析所有BIFF记录到内存
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] SST记录检测到，开始CONTINUE记录合并
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 遇到非CONTINUE记录($C400)，CONTINUE合并结束
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] SST记录合并完成，总CONTINUE记录: 0，最终长度: 8223
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] ReadRawRecord返回False，流结束
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 第一阶段完成，总记录数: 110
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 第二阶段：处理全局记录（BOUNDSHEET、SST等）
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 开始处理全局记录（BOUNDSHEET、SST等）
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #0: 类型=$0809
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #1: 类型=$0042
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #2: 类型=$0161
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #3: 类型=$003D
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #4: 类型=$0040
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #5: 类型=$0022
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #6: 类型=$000E
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #7: 类型=$000F
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #8: 类型=$009C
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #9: 类型=$00DA
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #10: 类型=$0031
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #11: 类型=$0031
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #12: 类型=$0031
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #13: 类型=$0031
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #14: 类型=$041E
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #15: 类型=$041E
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #16: 类型=$041E
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #17: 类型=$041E
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #18: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #19: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #20: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #21: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #22: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #23: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #24: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #25: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #26: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #27: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #28: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #29: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #30: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #31: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #32: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #33: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #34: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #35: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #36: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #37: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #38: 类型=$00E0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #39: 类型=$0293
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #40: 类型=$0293
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #41: 类型=$0293
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #42: 类型=$0293
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #43: 类型=$0293
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #44: 类型=$0293
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #45: 类型=$0092
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #46: 类型=$008C
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #47: 类型=$01C1
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 处理SST记录 #48
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] SST记录解析成功，字符串数量: 625
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 跳过全局记录 #49: 类型=$C400
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 全局记录处理完成，处理了 1 个关键记录
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 全局记录处理完成，处理了 1 个全局记录
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 第三阶段：处理工作表记录
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 开始处理工作表记录，从记录 #1 开始
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [淇℃伅] [UnifiedExcelProcessor_POI] 所有工作表记录处理完成，总单元格数: 0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:06:00] [璀﹀憡] [MainForm] Excel文件中没有工作表
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:20] [淇℃伅] [MainForm] Excel资源强制清理完成
鐢ㄦ埛鎿嶄綔: 资源管理
---
[2025-07-28 12:34:20] [淇℃伅] [MainForm] 打开Excel文件: vv_StdItem.xls
鐢ㄦ埛鎿嶄綔: 用户双击Excel节点
---
[2025-07-28 12:34:20] [淇℃伅] [MainForm] 开始加载Excel文件前的资源清理
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:20] [淇℃伅] [MainForm] 检查文件访问权限...
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:20] [淇℃伅] [MainForm] 文件访问正常，可以读取
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:20] [淇℃伅] [MainForm] 开始读取Excel文件: D:\MirServer\Mud2\DB\vv_StdItem.xls
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:20] [淇℃伅] [UnifiedExcelProcessor_POI] 开始搜索BIFF BOF记录($0809)，文件大小: 143360
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:20] [淇℃伅] [UnifiedExcelProcessor_POI] 检测到OLE2复合文档格式，尝试提取Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:20] [淇℃伅] [UnifiedExcelProcessor_POI] 在OLE2文档中搜索Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:20] [淇℃伅] [UnifiedExcelProcessor_POI] 找到Workbook流名称，位置: 1152
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:20] [淇℃伅] [UnifiedExcelProcessor_POI] 在Workbook流中找到BIFF BOF记录，位置: 6144
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:20] [淇℃伅] [UnifiedExcelProcessor_POI] 成功在OLE2文档中找到Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:20] [璀﹀憡] [MainForm] Excel文件中没有工作表
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:22] [淇℃伅] [MainForm] Excel资源强制清理完成
鐢ㄦ埛鎿嶄綔: 资源管理
---
[2025-07-28 12:34:22] [淇℃伅] [MainForm] 打开Excel文件: vv_Magic.xls
鐢ㄦ埛鎿嶄綔: 用户双击Excel节点
---
[2025-07-28 12:34:22] [淇℃伅] [MainForm] 开始加载Excel文件前的资源清理
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:22] [淇℃伅] [MainForm] 检查文件访问权限...
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:22] [淇℃伅] [MainForm] 文件访问正常，可以读取
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:22] [淇℃伅] [MainForm] 开始读取Excel文件: D:\MirServer\Mud2\DB\vv_Magic.xls
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] 开始搜索BIFF BOF记录($0809)，文件大小: 95744
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] 检测到OLE2复合文档格式，尝试提取Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] 在OLE2文档中搜索Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] 找到Workbook流名称，位置: 1152
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] 在Workbook流中找到BIFF BOF记录，位置: 6144
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] 成功在OLE2文档中找到Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] 处理BOUNDSHEET记录，数据长度: 18
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 解析到工作表名称="Magic"，BOF偏移=5617
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: BOF偏移=5617, 状态=0, 类型=0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 工作表"Magic"已创建，BOF偏移=5617
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 请求工作表索引=0，总工作表数=1
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 找到工作表"Magic"，开始转换
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 工作表"Magic"，行数=0，列数=0，单元格数=0
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 没有数据，创建空数据集
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:22] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 转换完成，记录数=0，字段数=1
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:22] [璀﹀憡] [MainForm] Excel文件中没有有效数据
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:24] [淇℃伅] [MainForm] Excel资源强制清理完成
鐢ㄦ埛鎿嶄綔: 资源管理
---
[2025-07-28 12:34:24] [淇℃伅] [MainForm] 打开Excel文件: vv_Moneys.xls
鐢ㄦ埛鎿嶄綔: 用户双击Excel节点
---
[2025-07-28 12:34:24] [淇℃伅] [MainForm] 开始加载Excel文件前的资源清理
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:24] [淇℃伅] [MainForm] 检查文件访问权限...
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:24] [淇℃伅] [MainForm] 文件访问正常，可以读取
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:24] [淇℃伅] [MainForm] 开始读取Excel文件: D:\MirServer\Mud2\DB\vv_Moneys.xls
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] 开始搜索BIFF BOF记录($0809)，文件大小: 5120
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] 检测到OLE2复合文档格式，尝试提取Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] 在OLE2文档中搜索Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] 找到Workbook流名称，位置: 1152
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] 在Workbook流中找到BIFF BOF记录，位置: 2048
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] 成功在OLE2文档中找到Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] 处理BOUNDSHEET记录，数据长度: 20
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 解析到工作表名称="Moneys"，BOF偏移=1662
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: BOF偏移=1662, 状态=0, 类型=0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 工作表"Moneys"已创建，BOF偏移=1662
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,0]: "MoneyID" (类型:0), 当前单元格数=0
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,1]: "Name" (类型:0), 当前单元格数=1
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,2]: "Stdmode" (类型:0), 当前单元格数=2
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,3]: "Shape" (类型:0), 当前单元格数=3
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,4]: "Weight" (类型:0), 当前单元格数=4
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,5]: "Anicount" (类型:0), 当前单元格数=5
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,6]: "Source" (类型:0), 当前单元格数=6
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,7]: "Reserved" (类型:0), 当前单元格数=7
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,8]: "Looks" (类型:0), 当前单元格数=8
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,9]: "DuraMax" (类型:0), 当前单元格数=9
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 请求工作表索引=0，总工作表数=1
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 找到工作表"Moneys"，开始转换
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 工作表"Moneys"，行数=6，列数=15，单元格数=90
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "MoneyID"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Name"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Stdmode"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Shape"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Weight"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Anicount"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Source"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Reserved"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Looks"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "DuraMax"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Price"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Stock"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Color"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "OverLap"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "LooksColor"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建数据集成功，字段数=15
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 数据转换完成，记录数=5
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 转换完成，记录数=5，字段数=15
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:24] [淇℃伅] [MainForm] Excel数据已通过TableDataManager加载，支持与数据库相同的编辑操作
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:24] [淇℃伅] [MainForm] Excel数据加载完成！
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:25] [淇℃伅] [MainForm] Excel资源强制清理完成
鐢ㄦ埛鎿嶄綔: 资源管理
---
[2025-07-28 12:34:25] [淇℃伅] [MainForm] 打开Excel文件: vv_Monster.xls
鐢ㄦ埛鎿嶄綔: 用户双击Excel节点
---
[2025-07-28 12:34:25] [淇℃伅] [MainForm] 开始加载Excel文件前的资源清理
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:25] [淇℃伅] [MainForm] 检查文件访问权限...
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:25] [淇℃伅] [MainForm] 文件访问正常，可以读取
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:25] [淇℃伅] [MainForm] 开始读取Excel文件: D:\MirServer\Mud2\DB\vv_Monster.xls
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] 开始搜索BIFF BOF记录($0809)，文件大小: 78336
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] 检测到OLE2复合文档格式，尝试提取Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] 在OLE2文档中搜索Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] 找到Workbook流名称，位置: 1152
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] 在Workbook流中找到BIFF BOF记录，位置: 6144
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] 成功在OLE2文档中找到Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] 处理BOUNDSHEET记录，数据长度: 22
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 解析到工作表名称="Monster"，BOF偏移=6239
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: BOF偏移=6239, 状态=0, 类型=0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 工作表"Monster"已创建，BOF偏移=6239
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 请求工作表索引=0，总工作表数=1
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 找到工作表"Monster"，开始转换
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 工作表"Monster"，行数=0，列数=0，单元格数=0
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 没有数据，创建空数据集
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:25] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 转换完成，记录数=0，字段数=1
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:25] [璀﹀憡] [MainForm] Excel文件中没有有效数据
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:36] [淇℃伅] [MainForm] Excel资源强制清理完成
鐢ㄦ埛鎿嶄綔: 资源管理
---
[2025-07-28 12:34:36] [淇℃伅] [MainForm] 打开Excel文件: vv_Moneys.xls
鐢ㄦ埛鎿嶄綔: 用户双击Excel节点
---
[2025-07-28 12:34:36] [淇℃伅] [MainForm] 开始加载Excel文件前的资源清理
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:36] [淇℃伅] [MainForm] 检查文件访问权限...
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:36] [淇℃伅] [MainForm] 文件访问正常，可以读取
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:36] [淇℃伅] [MainForm] 开始读取Excel文件: D:\MirServer\Mud2\DB\vv_Moneys.xls
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] 开始搜索BIFF BOF记录($0809)，文件大小: 5120
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] 检测到OLE2复合文档格式，尝试提取Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] 在OLE2文档中搜索Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] 找到Workbook流名称，位置: 1152
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] 在Workbook流中找到BIFF BOF记录，位置: 2048
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] 成功在OLE2文档中找到Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] 处理BOUNDSHEET记录，数据长度: 20
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 解析到工作表名称="Moneys"，BOF偏移=1662
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: BOF偏移=1662, 状态=0, 类型=0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 工作表"Moneys"已创建，BOF偏移=1662
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,0]: "MoneyID" (类型:0), 当前单元格数=0
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,1]: "Name" (类型:0), 当前单元格数=1
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,2]: "Stdmode" (类型:0), 当前单元格数=2
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,3]: "Shape" (类型:0), 当前单元格数=3
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,4]: "Weight" (类型:0), 当前单元格数=4
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,5]: "Anicount" (类型:0), 当前单元格数=5
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,6]: "Source" (类型:0), 当前单元格数=6
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,7]: "Reserved" (类型:0), 当前单元格数=7
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,8]: "Looks" (类型:0), 当前单元格数=8
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,9]: "DuraMax" (类型:0), 当前单元格数=9
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 请求工作表索引=0，总工作表数=1
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 找到工作表"Moneys"，开始转换
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 工作表"Moneys"，行数=6，列数=15，单元格数=90
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "MoneyID"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Name"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Stdmode"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Shape"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Weight"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Anicount"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Source"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Reserved"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Looks"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "DuraMax"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Price"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Stock"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Color"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "OverLap"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "LooksColor"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建数据集成功，字段数=15
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 数据转换完成，记录数=5
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 转换完成，记录数=5，字段数=15
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:34:36] [淇℃伅] [MainForm] Excel数据已通过TableDataManager加载，支持与数据库相同的编辑操作
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:34:36] [淇℃伅] [MainForm] Excel数据加载完成！
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:01] [淇℃伅] [MainForm] Excel资源强制清理完成
鐢ㄦ埛鎿嶄綔: 资源管理
---
[2025-07-28 12:55:01] [淇℃伅] [MainForm] 打开Excel文件: vv_StdItem.xls
鐢ㄦ埛鎿嶄綔: 用户双击Excel节点
---
[2025-07-28 12:55:01] [淇℃伅] [MainForm] 开始加载Excel文件前的资源清理
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:01] [淇℃伅] [MainForm] 检查文件访问权限...
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:01] [淇℃伅] [MainForm] 文件访问正常，可以读取
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:01] [淇℃伅] [MainForm] 开始读取Excel文件: D:\MirServer\Mud2\DB\vv_StdItem.xls
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:01] [淇℃伅] [UnifiedExcelProcessor_POI] 开始搜索BIFF BOF记录($0809)，文件大小: 143360
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:01] [淇℃伅] [UnifiedExcelProcessor_POI] 检测到OLE2复合文档格式，尝试提取Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:01] [淇℃伅] [UnifiedExcelProcessor_POI] 在OLE2文档中搜索Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:01] [淇℃伅] [UnifiedExcelProcessor_POI] 找到Workbook流名称，位置: 1152
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:01] [淇℃伅] [UnifiedExcelProcessor_POI] 在Workbook流中找到BIFF BOF记录，位置: 6144
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:01] [淇℃伅] [UnifiedExcelProcessor_POI] 成功在OLE2文档中找到Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:01] [璀﹀憡] [MainForm] Excel文件中没有工作表
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:09] [淇℃伅] [MainForm] Excel资源强制清理完成
鐢ㄦ埛鎿嶄綔: 资源管理
---
[2025-07-28 12:55:09] [淇℃伅] [MainForm] 打开Excel文件: vv_Magic.xls
鐢ㄦ埛鎿嶄綔: 用户双击Excel节点
---
[2025-07-28 12:55:09] [淇℃伅] [MainForm] 开始加载Excel文件前的资源清理
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:09] [淇℃伅] [MainForm] 检查文件访问权限...
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:09] [淇℃伅] [MainForm] 文件访问正常，可以读取
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:09] [淇℃伅] [MainForm] 开始读取Excel文件: D:\MirServer\Mud2\DB\vv_Magic.xls
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] 开始搜索BIFF BOF记录($0809)，文件大小: 95744
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] 检测到OLE2复合文档格式，尝试提取Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] 在OLE2文档中搜索Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] 找到Workbook流名称，位置: 1152
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] 在Workbook流中找到BIFF BOF记录，位置: 6144
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] 成功在OLE2文档中找到Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] 处理BOUNDSHEET记录，数据长度: 18
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 解析到工作表名称="Magic"，BOF偏移=5617
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: BOF偏移=5617, 状态=0, 类型=0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 工作表"Magic"已创建，BOF偏移=5617
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 请求工作表索引=0，总工作表数=1
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 找到工作表"Magic"，开始转换
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 工作表"Magic"，行数=0，列数=0，单元格数=0
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 没有数据，创建空数据集
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:09] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 转换完成，记录数=0，字段数=1
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:09] [璀﹀憡] [MainForm] Excel文件中没有有效数据
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:15] [淇℃伅] [MainForm] Excel资源强制清理完成
鐢ㄦ埛鎿嶄綔: 资源管理
---
[2025-07-28 12:55:15] [淇℃伅] [MainForm] 打开Excel文件: vv_Moneys.xls
鐢ㄦ埛鎿嶄綔: 用户双击Excel节点
---
[2025-07-28 12:55:15] [淇℃伅] [MainForm] 开始加载Excel文件前的资源清理
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:15] [淇℃伅] [MainForm] 检查文件访问权限...
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:15] [淇℃伅] [MainForm] 文件访问正常，可以读取
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:15] [淇℃伅] [MainForm] 开始读取Excel文件: D:\MirServer\Mud2\DB\vv_Moneys.xls
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] 开始搜索BIFF BOF记录($0809)，文件大小: 5120
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] 检测到OLE2复合文档格式，尝试提取Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] 在OLE2文档中搜索Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] 找到Workbook流名称，位置: 1152
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] 在Workbook流中找到BIFF BOF记录，位置: 2048
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] 成功在OLE2文档中找到Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] 处理BOUNDSHEET记录，数据长度: 20
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 解析到工作表名称="Moneys"，BOF偏移=1662
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: BOF偏移=1662, 状态=0, 类型=0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 工作表"Moneys"已创建，BOF偏移=1662
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,0]: "MoneyID" (类型:0), 当前单元格数=0
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,1]: "Name" (类型:0), 当前单元格数=1
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,2]: "Stdmode" (类型:0), 当前单元格数=2
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,3]: "Shape" (类型:0), 当前单元格数=3
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,4]: "Weight" (类型:0), 当前单元格数=4
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,5]: "Anicount" (类型:0), 当前单元格数=5
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,6]: "Source" (类型:0), 当前单元格数=6
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,7]: "Reserved" (类型:0), 当前单元格数=7
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,8]: "Looks" (类型:0), 当前单元格数=8
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] AddCell[0,9]: "DuraMax" (类型:0), 当前单元格数=9
鐢ㄦ埛鎿嶄綔: Excel解析调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 请求工作表索引=0，总工作表数=1
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 找到工作表"Moneys"，开始转换
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 工作表"Moneys"，行数=6，列数=15，单元格数=90
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "MoneyID"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Name"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Stdmode"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Shape"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Weight"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Anicount"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Source"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Reserved"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Looks"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "DuraMax"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Price"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Stock"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "Color"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "OverLap"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建字段[0] "LooksColor"
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 创建数据集成功，字段数=15
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 数据转换完成，记录数=5
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 转换完成，记录数=5，字段数=15
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:15] [淇℃伅] [MainForm] Excel数据已通过TableDataManager加载，支持与数据库相同的编辑操作
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:15] [淇℃伅] [MainForm] Excel数据加载完成！
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:17] [淇℃伅] [MainForm] Excel资源强制清理完成
鐢ㄦ埛鎿嶄綔: 资源管理
---
[2025-07-28 12:55:17] [淇℃伅] [MainForm] 打开Excel文件: vv_Monster.xls
鐢ㄦ埛鎿嶄綔: 用户双击Excel节点
---
[2025-07-28 12:55:17] [淇℃伅] [MainForm] 开始加载Excel文件前的资源清理
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:17] [淇℃伅] [MainForm] 检查文件访问权限...
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:17] [淇℃伅] [MainForm] 文件访问正常，可以读取
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:17] [淇℃伅] [MainForm] 开始读取Excel文件: D:\MirServer\Mud2\DB\vv_Monster.xls
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] 开始搜索BIFF BOF记录($0809)，文件大小: 78336
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] 检测到OLE2复合文档格式，尝试提取Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] 在OLE2文档中搜索Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] 找到Workbook流名称，位置: 1152
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] 在Workbook流中找到BIFF BOF记录，位置: 6144
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] 成功在OLE2文档中找到Workbook流
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] 处理BOUNDSHEET记录，数据长度: 22
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 解析到工作表名称="Monster"，BOF偏移=6239
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: BOF偏移=6239, 状态=0, 类型=0
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] BOUNDSHEET: 工作表"Monster"已创建，BOF偏移=6239
鐢ㄦ埛鎿嶄綔: 调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 请求工作表索引=0，总工作表数=1
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 找到工作表"Monster"，开始转换
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 工作表"Monster"，行数=0，列数=0，单元格数=0
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] ToDataSet: 没有数据，创建空数据集
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:17] [淇℃伅] [UnifiedExcelProcessor_POI] SheetToDataSet: 转换完成，记录数=0，字段数=1
鐢ㄦ埛鎿嶄綔: Excel转换调试
---
[2025-07-28 12:55:17] [璀﹀憡] [MainForm] Excel文件中没有有效数据
鐢ㄦ埛鎿嶄綔: 加载Excel文件
---
