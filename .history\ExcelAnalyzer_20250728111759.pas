program ExcelAnalyzer;

{$APPTYPE CONSOLE}

uses
  SysUtils, Classes;

type
  TBIFFRecord = record
    RecordType: Word;
    RecordLength: Word;
    Data: TBytes;
  end;

procedure AnalyzeExcelFile(const FileName: string);
var
  FileStream: TFileStream;
  RecordType, RecordLength: Word;
  RecordData: TBytes;
  RecordCount: Integer;
  Position: Int64;
  BOFOffset: LongWord;
  SheetName: string;
  NameLength: Byte;
  UnicodeFlag: Byte;
  I: Integer;
begin
  WriteLn('=== 分析文件: ', FileName, ' ===');
  
  try
    FileStream := TFileStream.Create(FileName, fmOpenRead);
    try
      WriteLn('文件大小: ', FileStream.Size, ' 字节');
      
      RecordCount := 0;
      Position := 0;
      
      while Position < FileStream.Size - 4 do
      begin
        FileStream.Position := Position;
        
        // 读取记录头
        FileStream.ReadBuffer(RecordType, 2);
        FileStream.ReadBuffer(RecordLength, 2);
        
        if RecordCount < 20 then
          WriteLn(Format('记录 #%d: 类型=$%04X, 长度=%d', [RecordCount, RecordType, RecordLength]));
        
        // 读取记录数据
        if RecordLength > 0 then
        begin
          SetLength(RecordData, RecordLength);
          if FileStream.Read(RecordData[0], RecordLength) <> RecordLength then
            Break;
        end
        else
          SetLength(RecordData, 0);
        
        // 分析特殊记录
        case RecordType of
          $0809: // BOF
            begin
              if Length(RecordData) >= 4 then
              begin
                WriteLn(Format('  -> BOF记录: BIFF版本=$%04X, 子流类型=$%04X', 
                  [PWord(@RecordData[0])^, PWord(@RecordData[2])^]));
              end;
            end;
            
          $0085: // BOUNDSHEET
            begin
              if Length(RecordData) >= 8 then
              begin
                BOFOffset := PLongWord(@RecordData[0])^;
                NameLength := RecordData[6];
                UnicodeFlag := RecordData[7];
                
                SheetName := '';
                if UnicodeFlag = 0 then
                begin
                  // ANSI编码
                  for I := 0 to NameLength - 1 do
                  begin
                    if (8 + I) < Length(RecordData) then
                      SheetName := SheetName + Chr(RecordData[8 + I]);
                  end;
                end
                else
                begin
                  // Unicode编码
                  for I := 0 to NameLength - 1 do
                  begin
                    if (8 + I * 2 + 1) < Length(RecordData) then
                      SheetName := SheetName + Chr(RecordData[8 + I * 2] or (RecordData[8 + I * 2 + 1] shl 8));
                  end;
                end;
                
                WriteLn(Format('  -> BOUNDSHEET记录: BOF偏移=%d, 名称长度=%d, 名称="%s"', 
                  [BOFOffset, NameLength, SheetName]));
                
                // 检查BOF偏移位置
                if BOFOffset < FileStream.Size - 8 then
                begin
                  FileStream.Position := BOFOffset;
                  FileStream.ReadBuffer(RecordType, 2);
                  FileStream.ReadBuffer(RecordLength, 2);
                  WriteLn(Format('  BOF位置的记录: 类型=$%04X, 长度=%d', [RecordType, RecordLength]));
                  
                  if RecordType = $0809 then
                  begin
                    if RecordLength >= 4 then
                    begin
                      FileStream.ReadBuffer(RecordType, 2); // BIFF版本
                      FileStream.ReadBuffer(RecordLength, 2); // 子流类型
                      WriteLn(Format('  BOF详情: BIFF版本=$%04X, 子流类型=$%04X', [RecordType, RecordLength]));
                    end;
                  end
                  else
                    WriteLn('  警告: BOF偏移位置不是有效的BOF记录!');
                end
                else
                  WriteLn('  错误: BOF偏移超出文件范围!');
              end;
            end;
        end;
        
        Position := Position + 4 + RecordLength;
        Inc(RecordCount);
        
        if RecordCount > 100 then
          Break;
      end;
      
      WriteLn('总记录数: ', RecordCount);
      
    finally
      FileStream.Free;
    end;
    
  except
    on E: Exception do
      WriteLn('分析文件时出错: ', E.Message);
  end;
  
  WriteLn;
end;

begin
  try
    AnalyzeExcelFile('testww\vv_Monster程序读不了.xls');
    AnalyzeExcelFile('testww\vv_Monster程序正常读取.xls');
    
    WriteLn('按回车键退出...');
    ReadLn;
  except
    on E: Exception do
      WriteLn('程序出错: ', E.Message);
  end;
end.
